'use client'

import React, { useState } from 'react'
import { useEthereumData } from '@/hooks/useEthereumData'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatPercentage, formatNumber } from '@/lib/utils'
import { ArrowUpIcon, ArrowDownIcon, WifiIcon, SignalSlashIcon } from '@heroicons/react/24/solid'
import { RefreshCw, Brain, TrendingUp, AlertTriangle, Activity } from 'lucide-react'

const EthereumDashboard: React.FC = () => {
  const {
    currentData,
    historicalData,
    analysis,
    alerts,
    loading,
    error,
    connectionStatus,
    refreshAll,
    fetchAnalysis,
  } = useEthereumData()

  const [analysisLoading, setAnalysisLoading] = useState(false)

  const handleAnalysisRequest = async (mode: string) => {
    setAnalysisLoading(true)
    try {
      await fetchAnalysis(mode)
    } finally {
      setAnalysisLoading(false)
    }
  }

  const ConnectionStatus = () => (
    <div className="flex items-center gap-2">
      {connectionStatus === 'connected' ? (
        <>
          <WifiIcon className="h-4 w-4 text-green-500" />
          <span className="text-sm text-green-500">Connected</span>
        </>
      ) : connectionStatus === 'disconnected' ? (
        <>
          <SignalSlashIcon className="h-4 w-4 text-red-500" />
          <span className="text-sm text-red-500">Disconnected</span>
        </>
      ) : (
        <>
          <RefreshCw className="h-4 w-4 animate-spin text-yellow-500" />
          <span className="text-sm text-yellow-500">Checking...</span>
        </>
      )}
    </div>
  )

  const PriceChangeIndicator = ({ change }: { change: number }) => (
    <div className={`flex items-center gap-1 ${change >= 0 ? 'text-green-500' : 'text-red-500'}`}>
      {change >= 0 ? (
        <ArrowUpIcon className="h-4 w-4" />
      ) : (
        <ArrowDownIcon className="h-4 w-4" />
      )}
      <span className="font-medium">{formatPercentage(change)}</span>
    </div>
  )

  if (error && connectionStatus === 'disconnected') {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="flex items-center justify-between p-6">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-6 w-6 text-red-500" />
              <div>
                <h3 className="font-semibold text-red-700">Backend Connection Error</h3>
                <p className="text-sm text-red-600">
                  Unable to connect to the Ethereum tracking backend. 
                  Please ensure the Python backend is running on localhost:8000
                </p>
              </div>
            </div>
            <Button onClick={refreshAll} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Ethereum Tracker</h1>
          <p className="text-muted-foreground">AI-powered real-time analysis dashboard</p>
        </div>
        <div className="flex items-center gap-4">
          <ConnectionStatus />
          <Button onClick={refreshAll} disabled={loading} variant="outline" size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Main Price Card */}
      {currentData && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Ethereum (ETH)</span>
              <Badge variant="secondary">Live</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <p className="text-sm text-muted-foreground mb-1">Current Price</p>
                <p className="text-3xl font-bold">{formatCurrency(currentData.price)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1">24h Change</p>
                <PriceChangeIndicator change={currentData.change_24h} />
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1">7d Change</p>
                <PriceChangeIndicator change={currentData.change_7d} />
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1">24h Volume</p>
                <p className="text-xl font-semibold">{formatCurrency(currentData.volume_24h)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* AI Analysis Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI Agent Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex gap-2">
                <Button
                  onClick={() => handleAnalysisRequest('comprehensive')}
                  disabled={analysisLoading}
                  size="sm"
                >
                  {analysisLoading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <TrendingUp className="h-4 w-4 mr-2" />
                  )}
                  Full Analysis
                </Button>
                <Button
                  onClick={() => handleAnalysisRequest('quick')}
                  disabled={analysisLoading}
                  variant="outline"
                  size="sm"
                >
                  Quick Check
                </Button>
              </div>
              
              {analysis.length > 0 ? (
                <div className="space-y-3">
                  {analysis.slice(0, 3).map((item, index) => (
                    <div key={index} className="border-l-2 border-blue-500 pl-4">
                      <div className="flex items-center justify-between mb-1">
                        <p className="font-medium text-sm">{item.agent}</p>
                        <Badge variant="outline">
                          {Math.round(item.confidence * 100)}% confidence
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {item.analysis}
                      </p>
                    </div>
                  ))}
                  {analysis.length > 3 && (
                    <p className="text-sm text-muted-foreground text-center">
                      +{analysis.length - 3} more analyses available
                    </p>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Click "Full Analysis" to get AI insights</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Alerts Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Active Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            {alerts.length > 0 ? (
              <div className="space-y-3">
                {alerts.slice(0, 5).map((alert, index) => (
                  <div 
                    key={index}
                    className={`p-3 rounded-lg border ${
                      alert.severity === 'critical' ? 'bg-red-50 border-red-200' :
                      alert.severity === 'high' ? 'bg-orange-50 border-orange-200' :
                      alert.severity === 'medium' ? 'bg-yellow-50 border-yellow-200' :
                      'bg-blue-50 border-blue-200'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <Badge 
                          variant={alert.severity === 'critical' ? 'destructive' : 'secondary'}
                          className="mb-2"
                        >
                          {alert.type.toUpperCase()}
                        </Badge>
                        <p className="text-sm">{alert.message}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No active alerts</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Market Stats */}
      {currentData && (
        <Card>
          <CardHeader>
            <CardTitle>Market Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-muted-foreground">Market Cap</p>
                <p className="text-lg font-semibold">
                  {formatCurrency(currentData.market_cap)}
                </p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-muted-foreground">24h Volume</p>
                <p className="text-lg font-semibold">
                  {formatCurrency(currentData.volume_24h)}
                </p>
              </div>
              {currentData.technical_indicators && (
                <>
                  <div className="text-center p-4 border rounded-lg">
                    <p className="text-sm text-muted-foreground">RSI</p>
                    <p className="text-lg font-semibold">
                      {currentData.technical_indicators.rsi.toFixed(1)}
                    </p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <p className="text-sm text-muted-foreground">MACD</p>
                    <p className="text-lg font-semibold">
                      {currentData.technical_indicators.macd.toFixed(4)}
                    </p>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default EthereumDashboard
